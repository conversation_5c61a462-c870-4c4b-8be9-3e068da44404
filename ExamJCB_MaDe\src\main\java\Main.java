import view.FrmQLSinhVien;
import javax.swing.UIManager;

public class Main {
    public static void main(String[] args) {
        try {
            // Thiết lập Look and Feel của hệ thống
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        // Chạy ứng dụng trên Event Dispatch Thread
        java.awt.EventQueue.invokeLater(() -> {
            new FrmQLSinhVien().setVisible(true);
        });
    }
}
