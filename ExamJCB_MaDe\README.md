# Ứng Dụng Quản Lý <PERSON> Viên (ExamJCB_02)

## Mô tả
Ứng dụng Java Swing để quản lý thông tin sinh viên với các chức năng CRUD cơ bản và tìm kiếm.

## Cấu trúc dự án
```
src/main/java/
├── Main.java                    # Class chính để chạy ứng dụng
├── model/
│   └── SinhVien.java           # Model class cho Sinh Viên
├── dao/
│   └── SinhVienDAO.java        # Data Access Object cho Sinh Viên
├── util/
│   └── DBConnection.java       # Kết nối cơ sở dữ liệu
└── view/
    ├── FrmQLSinhVien.java      # Giao diện chính
    └── FrmQLSinhVien.form      # Form design file
```

## Yêu cầu hệ thống
- Java 11 hoặc cao hơn
- Maven 3.6+
- SQL Server (với database QLSV)

## Cài đặt và chạy

### 1. Thiết lập cơ sở dữ liệu
Chạy script SQL trong file `scripts_db( de 02).sql` để tạo database và bảng:
```sql
-- Tạo database QLSV
-- Tạo bảng SinhVien với các cột: MaSV, HoTen, Tuoi, GioiTinh
-- Thêm dữ liệu mẫu
```

### 2. Cấu hình kết nối database
Chỉnh sửa file `src/main/java/util/DBConnection.java`:
```java
static String url = "***************************************************************";
static String user = "sa";        // Thay đổi username
static String pass = "sa";        // Thay đổi password
```

### 3. Biên dịch và chạy
```bash
# Biên dịch dự án
mvn clean compile

# Chạy ứng dụng
mvn exec:java -Dexec.mainClass="Main"
```

## Chức năng

### 1. Quản lý sinh viên
- **Thêm**: Nhập thông tin sinh viên mới và click "Thêm"
- **Sửa**: Click vào dòng trong bảng, chỉnh sửa thông tin và click "Sửa"
- **Xóa**: Click vào dòng trong bảng và click "Xóa"
- **Nhập mới**: Xóa toàn bộ thông tin trên form

### 2. Tìm kiếm và lọc
- **Tìm theo tên**: Nhập tên vào ô "Tìm Tên" và click "Tìm"
- **Lọc theo giới tính**: Chọn giới tính trong ComboBox

### 3. Giao diện
- Bảng hiển thị danh sách sinh viên
- Form nhập liệu với các trường: Mã SV, Họ Tên, Tuổi, Giới Tính
- Các nút chức năng: Thêm, Sửa, Xóa, Nhập Mới, Tìm
- ComboBox lọc theo giới tính

## Lưu ý
- Mã sinh viên tự động tăng (IDENTITY)
- Giới tính chỉ nhận giá trị "Nam" hoặc "Nữ"
- Tuổi phải là số nguyên dương
- Họ tên không được để trống

## Cấu trúc Database
```sql
CREATE TABLE SinhVien (
    MaSV INT IDENTITY(1,1) PRIMARY KEY,
    HoTen NVARCHAR(100) NOT NULL,
    Tuoi INT NOT NULL,
    GioiTinh NVARCHAR(10) NOT NULL CHECK (GioiTinh IN (N'Nam', N'Nữ'))
);
```
