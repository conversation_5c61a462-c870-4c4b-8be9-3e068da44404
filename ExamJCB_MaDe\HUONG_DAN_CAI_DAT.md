# HƯỚNG DẪN CÀI ĐẶT VÀ CHẠY ỨNG DỤNG QUẢN LÝ SINH VIÊN

## Bước 1: <PERSON><PERSON><PERSON> bị môi trường

### 1.1. Cài đặt Java JDK 11+
- T<PERSON>i và cài đặt Java JDK 11 hoặc cao hơn
- <PERSON><PERSON><PERSON> tra: `java -version`

### 1.2. <PERSON><PERSON><PERSON> đặt Maven
- Tải và cài đặt Apache Maven
- Kiểm tra: `mvn -version`

### 1.3. Cài đặt SQL Server
- Cài đặt SQL Server (Express hoặc Developer)
- Cài đặt SQL Server Management Studio (SSMS)

## Bước 2: Thiết lập Database

### 2.1. Tạo Database
1. Mở SQL Server Management Studio
2. Kết nối đến SQL Server instance
3. Chạy script trong file `scripts_db( de 02).sql`:

```sql
CREATE DATABASE QLSV;
GO

USE QLSV;
GO

CREATE TABLE SinhVien (
    MaSV INT IDENTITY(1,1) PRIMARY KEY,
    HoTen NVARCHAR(100) NOT NULL,
    <PERSON><PERSON> INT NOT NULL,
    GioiTinh NVARCHAR(10) NOT NULL CHECK (GioiTinh IN (N'Nam', N'Nữ'))
);
GO

INSERT INTO SinhVien (<PERSON>Ten, <PERSON>oi, GioiTinh) VALUES
(N'Nguyễn Văn An', 20, N'Nam'),
(N'Trần Thị Bình', 21, N'Nữ'),
(N'Lê Văn <PERSON>ường', 19, N'<PERSON>'),
(N'Phạm Thị <PERSON>ng', 22, N'Nữ');
GO
```

### 2.2. <PERSON>ấu hình kết nối
Chỉnh sửa file `src/main/java/util/DBConnection.java`:
```java
static String url = "***************************************************************";
static String user = "sa";        // Thay bằng username của bạn
static String pass = "your_password"; // Thay bằng password của bạn
```

## Bước 3: Chạy ứng dụng

### Cách 1: Sử dụng file batch (Windows)
```bash
run.bat
```

### Cách 2: Sử dụng Maven
```bash
# Biên dịch
mvn clean compile

# Chạy ứng dụng
mvn exec:java -Dexec.mainClass="Main"
```

### Cách 3: Test kết nối trước
```bash
# Test kết nối database
mvn exec:java -Dexec.mainClass="TestConnection"
```

## Bước 4: Sử dụng ứng dụng

### 4.1. Thêm sinh viên mới
1. Nhập họ tên, tuổi
2. Chọn giới tính (Nam/Nữ)
3. Click nút "Thêm"

### 4.2. Sửa thông tin sinh viên
1. Click vào dòng sinh viên trong bảng
2. Chỉnh sửa thông tin trên form
3. Click nút "Sửa"

### 4.3. Xóa sinh viên
1. Click vào dòng sinh viên trong bảng
2. Click nút "Xóa"
3. Xác nhận xóa

### 4.4. Tìm kiếm
- Nhập tên vào ô "Tìm Tên" và click "Tìm"
- Chọn giới tính trong ComboBox để lọc

### 4.5. Nhập mới
- Click "Nhập Mới" để xóa toàn bộ form

## Xử lý lỗi thường gặp

### Lỗi kết nối database
- Kiểm tra SQL Server đã chạy chưa
- Kiểm tra username/password trong DBConnection.java
- Kiểm tra tên database đã tạo chưa

### Lỗi biên dịch
- Kiểm tra Java JDK đã cài đặt đúng chưa
- Kiểm tra Maven đã cài đặt đúng chưa
- Chạy `mvn clean` trước khi compile

### Lỗi dependency
- Kiểm tra kết nối internet
- Chạy `mvn dependency:resolve`

## Cấu trúc thư mục
```
ExamJCB_MaDe/
├── src/main/java/
│   ├── Main.java
│   ├── TestConnection.java
│   ├── model/SinhVien.java
│   ├── dao/SinhVienDAO.java
│   ├── util/DBConnection.java
│   └── view/FrmQLSinhVien.java
├── scripts_db( de 02).sql
├── pom.xml
├── run.bat
└── README.md
```
