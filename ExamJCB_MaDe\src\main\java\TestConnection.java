import util.DBConnection;
import dao.SinhVienDAO;
import model.SinhVien;
import java.util.List;

/**
 * Class test kết nối database và các chức năng cơ bản
 */
public class TestConnection {
    public static void main(String[] args) {
        System.out.println("=== TEST KẾT NỐI DATABASE ===");
        
        // Test kết nối
        if (DBConnection.getConnection() != null) {
            System.out.println("✓ Kết nối database thành công!");
        } else {
            System.out.println("✗ Kết nối database thất bại!");
            return;
        }
        
        // Test DAO
        SinhVienDAO dao = new SinhVienDAO();
        
        System.out.println("\n=== TEST CHỨC NĂNG DAO ===");
        
        // Test lấy danh sách
        List<SinhVien> list = dao.getAll();
        System.out.println("✓ Lấy danh sách sinh viên: " + list.size() + " sinh viên");
        
        // Hiển thị danh sách
        System.out.println("\nDanh sách sinh viên:");
        for (SinhVien sv : list) {
            System.out.println("- " + sv.toString());
        }
        
        // Test tìm kiếm
        List<SinhVien> searchResult = dao.searchByName("An");
        System.out.println("\n✓ Tìm kiếm theo tên 'An': " + searchResult.size() + " kết quả");
        
        // Test lọc theo giới tính
        List<SinhVien> maleStudents = dao.filterByGender("Nam");
        System.out.println("✓ Lọc sinh viên Nam: " + maleStudents.size() + " sinh viên");
        
        List<SinhVien> femaleStudents = dao.filterByGender("Nữ");
        System.out.println("✓ Lọc sinh viên Nữ: " + femaleStudents.size() + " sinh viên");
        
        System.out.println("\n=== TEST HOÀN TẤT ===");
        System.out.println("Tất cả chức năng hoạt động bình thường!");
    }
}
