<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.5" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
    <Property name="title" type="java.lang.String" value="Giao di&#x1ec7;n qu&#x1ea3;n l&#xfd; sinh vi&#xea;n"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="true"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" attributes="0">
                      <EmptySpace min="6" pref="6" max="-2" attributes="0"/>
                      <Component id="jPanel2" max="32767" attributes="0"/>
                  </Group>
                  <Group type="102" attributes="0">
                      <Component id="rdbNam" min="-2" max="-2" attributes="0"/>
                      <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
                  </Group>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Component id="rdbNam" min="-2" max="-2" attributes="0"/>
              <EmptySpace type="unrelated" max="-2" attributes="0"/>
              <Component id="jPanel2" max="32767" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Container class="javax.swing.JPanel" name="rdbNam">

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" pref="32" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="0" attributes="0">
                      <Component id="jLabel1" alignment="0" min="-2" max="-2" attributes="0"/>
                      <Component id="jLabel2" alignment="0" min="-2" max="-2" attributes="0"/>
                      <Component id="jLabel3" alignment="0" min="-2" max="-2" attributes="0"/>
                      <Component id="jLabel4" alignment="0" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace min="-2" pref="77" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="0" attributes="0">
                      <Group type="102" attributes="0">
                          <Component id="jRadioButton1" min="-2" max="-2" attributes="0"/>
                          <EmptySpace min="-2" pref="62" max="-2" attributes="0"/>
                          <Component id="rdbNu" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <Group type="103" alignment="0" groupAlignment="1" max="-2" attributes="0">
                          <Component id="txtHoTen" pref="400" max="32767" attributes="0"/>
                          <Component id="txtMaSV" max="32767" attributes="0"/>
                      </Group>
                      <Component id="txtTuoi" alignment="0" min="-2" pref="400" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace min="-2" pref="123" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="0" max="-2" attributes="0">
                      <Component id="btnNhapMoi" max="32767" attributes="0"/>
                      <Component id="btnXoa" alignment="0" max="32767" attributes="0"/>
                      <Component id="btnSua" alignment="0" max="32767" attributes="0"/>
                      <Component id="btnThem" alignment="0" max="32767" attributes="0"/>
                  </Group>
                  <EmptySpace min="195" pref="54" max="32767" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <Group type="103" groupAlignment="0" attributes="0">
                      <Group type="102" attributes="0">
                          <EmptySpace min="-2" pref="34" max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="jLabel1" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="txtMaSV" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                          </Group>
                      </Group>
                      <Group type="102" alignment="0" attributes="0">
                          <EmptySpace min="-2" pref="30" max="-2" attributes="0"/>
                          <Component id="btnThem" min="-2" max="-2" attributes="0"/>
                      </Group>
                  </Group>
                  <Group type="103" groupAlignment="0" attributes="0">
                      <Group type="102" attributes="0">
                          <EmptySpace min="-2" pref="33" max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="jLabel2" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="txtHoTen" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                          </Group>
                      </Group>
                      <Group type="102" alignment="0" attributes="0">
                          <EmptySpace min="-2" pref="24" max="-2" attributes="0"/>
                          <Component id="btnSua" min="-2" max="-2" attributes="0"/>
                      </Group>
                  </Group>
                  <Group type="103" groupAlignment="1" attributes="0">
                      <Group type="102" alignment="1" attributes="0">
                          <EmptySpace min="-2" pref="36" max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="jLabel3" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="txtTuoi" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace min="-2" pref="36" max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="jLabel4" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="jRadioButton1" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="rdbNu" alignment="3" min="-2" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace pref="22" max="32767" attributes="0"/>
                      </Group>
                      <Group type="102" alignment="1" attributes="0">
                          <EmptySpace max="32767" attributes="0"/>
                          <Component id="btnXoa" min="-2" max="-2" attributes="0"/>
                          <EmptySpace min="-2" pref="39" max="-2" attributes="0"/>
                          <Component id="btnNhapMoi" min="-2" max="-2" attributes="0"/>
                          <EmptySpace min="-2" pref="29" max="-2" attributes="0"/>
                      </Group>
                  </Group>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Component class="javax.swing.JLabel" name="jLabel1">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="14" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="M&#xe3; Sinh Vi&#xea;n"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel2">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="14" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="H&#x1ecd; T&#xea;n Sinh Vi&#xea;n "/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel3">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="14" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Tu&#x1ed5;i "/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel4">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="14" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Gi&#x1edb;i T&#xed;nh "/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="txtMaSV">
        </Component>
        <Component class="javax.swing.JTextField" name="txtHoTen">
        </Component>
        <Component class="javax.swing.JTextField" name="txtTuoi">
        </Component>
        <Component class="javax.swing.JButton" name="btnThem">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="14" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Th&#xea;m "/>
          </Properties>
        </Component>
        <Component class="javax.swing.JButton" name="btnSua">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="14" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="S&#x1eed;a"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JButton" name="btnXoa">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="14" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Xo&#xe1;"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JButton" name="btnNhapMoi">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="14" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Nh&#x1ead;p M&#x1edb;i"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JRadioButton" name="jRadioButton1">
          <Properties>
            <Property name="text" type="java.lang.String" value="Nam"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JRadioButton" name="rdbNu">
          <Properties>
            <Property name="text" type="java.lang.String" value="N&#x1eef;"/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Container class="javax.swing.JPanel" name="jPanel2">

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="1" attributes="0">
                  <Component id="jScrollPane1" max="32767" attributes="0"/>
                  <EmptySpace max="-2" attributes="0"/>
              </Group>
              <Group type="102" alignment="1" attributes="0">
                  <EmptySpace min="-2" pref="32" max="-2" attributes="0"/>
                  <Component id="jLabel5" min="-2" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="35" max="-2" attributes="0"/>
                  <Component id="txtTimTen" min="-2" pref="226" max="-2" attributes="0"/>
                  <EmptySpace max="32767" attributes="0"/>
                  <Component id="btnTim" min="-2" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="67" max="-2" attributes="0"/>
                  <Component id="jLabel6" min="-2" pref="117" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="27" max="-2" attributes="0"/>
                  <Component id="cboLocTheoGioiTinh" min="-2" pref="199" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="48" max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace max="-2" attributes="0"/>
                  <Component id="jScrollPane1" pref="322" max="32767" attributes="0"/>
                  <EmptySpace type="separate" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="cboLocTheoGioiTinh" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="jLabel5" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="btnTim" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="jLabel6" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="txtTimTen" alignment="3" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace min="-2" pref="34" max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Container class="javax.swing.JScrollPane" name="jScrollPane1">
          <AuxValues>
            <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
          </AuxValues>

          <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
          <SubComponents>
            <Component class="javax.swing.JTable" name="tblSinhVien">
              <Properties>
                <Property name="model" type="javax.swing.table.TableModel" editor="org.netbeans.modules.form.editors2.TableModelEditor">
                  <Table columnCount="4" rowCount="4">
                    <Column editable="true" title="M&#xe3; Sinh Vi&#xea;n" type="java.lang.Object"/>
                    <Column editable="true" title="H&#x1ecd; T&#xea;n " type="java.lang.Object"/>
                    <Column editable="true" title="Tu&#x1ed5;i" type="java.lang.Object"/>
                    <Column editable="true" title="Gi&#x1edb;i T&#xed;nh " type="java.lang.Object"/>
                  </Table>
                </Property>
                <Property name="columnModel" type="javax.swing.table.TableColumnModel" editor="org.netbeans.modules.form.editors2.TableColumnModelEditor">
                  <TableColumnModel selectionModel="0">
                    <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                      <Title/>
                      <Editor/>
                      <Renderer/>
                    </Column>
                    <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                      <Title/>
                      <Editor/>
                      <Renderer/>
                    </Column>
                    <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                      <Title/>
                      <Editor/>
                      <Renderer/>
                    </Column>
                    <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                      <Title/>
                      <Editor/>
                      <Renderer/>
                    </Column>
                  </TableColumnModel>
                </Property>
                <Property name="tableHeader" type="javax.swing.table.JTableHeader" editor="org.netbeans.modules.form.editors2.JTableHeaderEditor">
                  <TableHeader reorderingAllowed="true" resizingAllowed="true"/>
                </Property>
              </Properties>
            </Component>
          </SubComponents>
        </Container>
        <Component class="javax.swing.JComboBox" name="cboLocTheoGioiTinh">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="14" style="0"/>
            </Property>
            <Property name="model" type="javax.swing.ComboBoxModel" editor="org.netbeans.modules.form.editors2.ComboBoxModelEditor">
              <StringArray count="4">
                <StringItem index="0" value="Item 1"/>
                <StringItem index="1" value="Item 2"/>
                <StringItem index="2" value="Item 3"/>
                <StringItem index="3" value="Item 4"/>
              </StringArray>
            </Property>
          </Properties>
          <AuxValues>
            <AuxValue name="JavaCodeGenerator_TypeParameters" type="java.lang.String" value="&lt;String&gt;"/>
          </AuxValues>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel5">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="14" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="T&#xec;m T&#xea;n"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JButton" name="btnTim">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="14" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="T&#xec;m"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel6">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="14" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="L&#x1ecd;c Theo Gi&#x1edb;i T&#xed;nh"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="txtTimTen">
        </Component>
      </SubComponents>
    </Container>
  </SubComponents>
</Form>
