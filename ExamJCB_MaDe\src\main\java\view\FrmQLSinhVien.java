/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/GUIForms/JFrame.java to edit this template
 */
package view;

import dao.SinhVienDAO;
import model.SinhVien;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class FrmQLSinhVien extends javax.swing.JFrame {

    private SinhVienDAO sinhVienDAO;
    private DefaultTableModel tableModel;
    private ButtonGroup genderGroup;

    /**
     * Creates new form FrmQLSanPham
     */
    public FrmQLSinhVien() {
        initComponents();
        sinhVienDAO = new SinhVienDAO();
        setupTable();
        setupGenderRadioButtons();
        setupComboBox();
        loadData();
        addEventHandlers();
    }

    /**
     * This method is called from within the constructor to initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is always
     * regenerated by the Form Editor.
     */
    @SuppressWarnings("unchecked")
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        rdbNam = new javax.swing.JPanel();
        jLabel1 = new javax.swing.JLabel();
        jLabel2 = new javax.swing.JLabel();
        jLabel3 = new javax.swing.JLabel();
        jLabel4 = new javax.swing.JLabel();
        txtMaSV = new javax.swing.JTextField();
        txtHoTen = new javax.swing.JTextField();
        txtTuoi = new javax.swing.JTextField();
        btnThem = new javax.swing.JButton();
        btnSua = new javax.swing.JButton();
        btnXoa = new javax.swing.JButton();
        btnNhapMoi = new javax.swing.JButton();
        jRadioButton1 = new javax.swing.JRadioButton();
        rdbNu = new javax.swing.JRadioButton();
        jPanel2 = new javax.swing.JPanel();
        jScrollPane1 = new javax.swing.JScrollPane();
        tblSinhVien = new javax.swing.JTable();
        cboLocTheoGioiTinh = new javax.swing.JComboBox<>();
        jLabel5 = new javax.swing.JLabel();
        btnTim = new javax.swing.JButton();
        jLabel6 = new javax.swing.JLabel();
        txtTimTen = new javax.swing.JTextField();

        setDefaultCloseOperation(javax.swing.WindowConstants.EXIT_ON_CLOSE);
        setTitle("Giao diện quản lý sinh viên");

        jLabel1.setFont(new java.awt.Font("Segoe UI", 0, 14)); // NOI18N
        jLabel1.setText("Mã Sinh Viên");

        jLabel2.setFont(new java.awt.Font("Segoe UI", 0, 14)); // NOI18N
        jLabel2.setText("Họ Tên Sinh Viên ");

        jLabel3.setFont(new java.awt.Font("Segoe UI", 0, 14)); // NOI18N
        jLabel3.setText("Tuổi ");

        jLabel4.setFont(new java.awt.Font("Segoe UI", 0, 14)); // NOI18N
        jLabel4.setText("Giới Tính ");

        btnThem.setFont(new java.awt.Font("Segoe UI", 0, 14)); // NOI18N
        btnThem.setText("Thêm ");

        btnSua.setFont(new java.awt.Font("Segoe UI", 0, 14)); // NOI18N
        btnSua.setText("Sửa");

        btnXoa.setFont(new java.awt.Font("Segoe UI", 0, 14)); // NOI18N
        btnXoa.setText("Xoá");

        btnNhapMoi.setFont(new java.awt.Font("Segoe UI", 0, 14)); // NOI18N
        btnNhapMoi.setText("Nhập Mới");

        jRadioButton1.setText("Nam");

        rdbNu.setText("Nữ");

        javax.swing.GroupLayout rdbNamLayout = new javax.swing.GroupLayout(rdbNam);
        rdbNam.setLayout(rdbNamLayout);
        rdbNamLayout.setHorizontalGroup(
            rdbNamLayout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(rdbNamLayout.createSequentialGroup()
                .addGap(32, 32, 32)
                .addGroup(rdbNamLayout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addComponent(jLabel1)
                    .addComponent(jLabel2)
                    .addComponent(jLabel3)
                    .addComponent(jLabel4))
                .addGap(77, 77, 77)
                .addGroup(rdbNamLayout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addGroup(rdbNamLayout.createSequentialGroup()
                        .addComponent(jRadioButton1)
                        .addGap(62, 62, 62)
                        .addComponent(rdbNu))
                    .addGroup(rdbNamLayout.createParallelGroup(javax.swing.GroupLayout.Alignment.TRAILING, false)
                        .addComponent(txtHoTen, javax.swing.GroupLayout.DEFAULT_SIZE, 400, Short.MAX_VALUE)
                        .addComponent(txtMaSV))
                    .addComponent(txtTuoi, javax.swing.GroupLayout.PREFERRED_SIZE, 400, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(123, 123, 123)
                .addGroup(rdbNamLayout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING, false)
                    .addComponent(btnNhapMoi, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                    .addComponent(btnXoa, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                    .addComponent(btnSua, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                    .addComponent(btnThem, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
                .addGap(54, 54, Short.MAX_VALUE))
        );
        rdbNamLayout.setVerticalGroup(
            rdbNamLayout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(rdbNamLayout.createSequentialGroup()
                .addGroup(rdbNamLayout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addGroup(rdbNamLayout.createSequentialGroup()
                        .addGap(34, 34, 34)
                        .addGroup(rdbNamLayout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                            .addComponent(jLabel1)
                            .addComponent(txtMaSV, javax.swing.GroupLayout.PREFERRED_SIZE, 30, javax.swing.GroupLayout.PREFERRED_SIZE)))
                    .addGroup(rdbNamLayout.createSequentialGroup()
                        .addGap(30, 30, 30)
                        .addComponent(btnThem)))
                .addGroup(rdbNamLayout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addGroup(rdbNamLayout.createSequentialGroup()
                        .addGap(33, 33, 33)
                        .addGroup(rdbNamLayout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                            .addComponent(jLabel2)
                            .addComponent(txtHoTen, javax.swing.GroupLayout.PREFERRED_SIZE, 30, javax.swing.GroupLayout.PREFERRED_SIZE)))
                    .addGroup(rdbNamLayout.createSequentialGroup()
                        .addGap(24, 24, 24)
                        .addComponent(btnSua)))
                .addGroup(rdbNamLayout.createParallelGroup(javax.swing.GroupLayout.Alignment.TRAILING)
                    .addGroup(rdbNamLayout.createSequentialGroup()
                        .addGap(36, 36, 36)
                        .addGroup(rdbNamLayout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                            .addComponent(jLabel3)
                            .addComponent(txtTuoi, javax.swing.GroupLayout.PREFERRED_SIZE, 30, javax.swing.GroupLayout.PREFERRED_SIZE))
                        .addGap(36, 36, 36)
                        .addGroup(rdbNamLayout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                            .addComponent(jLabel4)
                            .addComponent(jRadioButton1)
                            .addComponent(rdbNu))
                        .addContainerGap(22, Short.MAX_VALUE))
                    .addGroup(rdbNamLayout.createSequentialGroup()
                        .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                        .addComponent(btnXoa)
                        .addGap(39, 39, 39)
                        .addComponent(btnNhapMoi)
                        .addGap(29, 29, 29))))
        );

        tblSinhVien.setModel(new javax.swing.table.DefaultTableModel(
            new Object [][] {
                {null, null, null, null},
                {null, null, null, null},
                {null, null, null, null},
                {null, null, null, null}
            },
            new String [] {
                "Mã Sinh Viên", "Họ Tên ", "Tuổi", "Giới Tính "
            }
        ));
        jScrollPane1.setViewportView(tblSinhVien);

        cboLocTheoGioiTinh.setFont(new java.awt.Font("Segoe UI", 0, 14)); // NOI18N
        cboLocTheoGioiTinh.setModel(new javax.swing.DefaultComboBoxModel<>(new String[] { "Item 1", "Item 2", "Item 3", "Item 4" }));

        jLabel5.setFont(new java.awt.Font("Segoe UI", 0, 14)); // NOI18N
        jLabel5.setText("Tìm Tên");

        btnTim.setFont(new java.awt.Font("Segoe UI", 0, 14)); // NOI18N
        btnTim.setText("Tìm");

        jLabel6.setFont(new java.awt.Font("Segoe UI", 0, 14)); // NOI18N
        jLabel6.setText("Lọc Theo Giới Tính");

        javax.swing.GroupLayout jPanel2Layout = new javax.swing.GroupLayout(jPanel2);
        jPanel2.setLayout(jPanel2Layout);
        jPanel2Layout.setHorizontalGroup(
            jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, jPanel2Layout.createSequentialGroup()
                .addComponent(jScrollPane1)
                .addContainerGap())
            .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, jPanel2Layout.createSequentialGroup()
                .addGap(32, 32, 32)
                .addComponent(jLabel5)
                .addGap(35, 35, 35)
                .addComponent(txtTimTen, javax.swing.GroupLayout.PREFERRED_SIZE, 226, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                .addComponent(btnTim)
                .addGap(67, 67, 67)
                .addComponent(jLabel6, javax.swing.GroupLayout.PREFERRED_SIZE, 117, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(27, 27, 27)
                .addComponent(cboLocTheoGioiTinh, javax.swing.GroupLayout.PREFERRED_SIZE, 199, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(48, 48, 48))
        );
        jPanel2Layout.setVerticalGroup(
            jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(jPanel2Layout.createSequentialGroup()
                .addContainerGap()
                .addComponent(jScrollPane1, javax.swing.GroupLayout.DEFAULT_SIZE, 322, Short.MAX_VALUE)
                .addGap(18, 18, 18)
                .addGroup(jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(cboLocTheoGioiTinh, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(jLabel5)
                    .addComponent(btnTim)
                    .addComponent(jLabel6)
                    .addComponent(txtTimTen, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(34, 34, 34))
        );

        javax.swing.GroupLayout layout = new javax.swing.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addContainerGap()
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addGroup(layout.createSequentialGroup()
                        .addGap(6, 6, 6)
                        .addComponent(jPanel2, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
                    .addGroup(layout.createSequentialGroup()
                        .addComponent(rdbNam, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                        .addGap(0, 0, Short.MAX_VALUE)))
                .addContainerGap())
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addContainerGap()
                .addComponent(rdbNam, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.UNRELATED)
                .addComponent(jPanel2, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                .addContainerGap())
        );

        pack();
        setLocationRelativeTo(null);
    }// </editor-fold>//GEN-END:initComponents

    
    
    
    
    
    
    // Thiết lập bảng
    private void setupTable() {
        String[] columnNames = {"Mã SV", "Họ Tên", "Tuổi", "Giới Tính"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // Không cho phép chỉnh sửa trực tiếp trên bảng
            }
        };
        tblSinhVien.setModel(tableModel);

        // Thêm sự kiện click vào bảng
        tblSinhVien.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                tblSinhVienMouseClicked(evt);
            }
        });
    }

    // Thiết lập radio button cho giới tính
    private void setupGenderRadioButtons() {
        genderGroup = new ButtonGroup();
        genderGroup.add(jRadioButton1); // Nam
        genderGroup.add(rdbNu); // Nữ
        jRadioButton1.setSelected(true); // Mặc định chọn Nam
    }

    // Thiết lập ComboBox
    private void setupComboBox() {
        cboLocTheoGioiTinh.removeAllItems();
        cboLocTheoGioiTinh.addItem("Tất cả");
        cboLocTheoGioiTinh.addItem("Nam");
        cboLocTheoGioiTinh.addItem("Nữ");
    }

    // Load dữ liệu từ database
    private void loadData() {
        List<SinhVien> list = sinhVienDAO.getAll();
        displayData(list);
    }

    // Hiển thị dữ liệu lên bảng
    private void displayData(List<SinhVien> list) {
        tableModel.setRowCount(0);
        for (SinhVien sv : list) {
            Object[] row = {sv.getMaSV(), sv.getHoTen(), sv.getTuoi(), sv.getGioiTinh()};
            tableModel.addRow(row);
        }
    }

    // Thêm các sự kiện cho các nút 
    private void addEventHandlers() {
        btnThem.addActionListener(e -> themSinhVien());
        btnSua.addActionListener(e -> suaSinhVien());
        btnXoa.addActionListener(e -> xoaSinhVien());
        btnNhapMoi.addActionListener(e -> nhapMoi());
        btnTim.addActionListener(e -> timKiem());
        cboLocTheoGioiTinh.addActionListener(e -> locTheoGioiTinh());
    }

    /**
     * @param args the command line arguments
     */
    public static void main(String args[]) {
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
        } catch (Exception e) {
            e.printStackTrace();
        }

        java.awt.EventQueue.invokeLater(() -> {
            new FrmQLSinhVien().setVisible(true);
        });
    }

    // Sự kiện click vào bảng
    private void tblSinhVienMouseClicked(java.awt.event.MouseEvent evt) {
        int selectedRow = tblSinhVien.getSelectedRow();
        if (selectedRow >= 0) {
            txtMaSV.setText(tableModel.getValueAt(selectedRow, 0).toString());
            txtHoTen.setText(tableModel.getValueAt(selectedRow, 1).toString());
            txtTuoi.setText(tableModel.getValueAt(selectedRow, 2).toString());
            String gioiTinh = tableModel.getValueAt(selectedRow, 3).toString();
            if ("Nam".equals(gioiTinh)) {
                jRadioButton1.setSelected(true);
            } else {
                rdbNu.setSelected(true);
            }
        }
    }

    // Thêm sinh viên
    private void themSinhVien() {
        try {
            String hoTen = txtHoTen.getText().trim();
            String tuoiStr = txtTuoi.getText().trim();

            if (hoTen.isEmpty() || tuoiStr.isEmpty()) {
                JOptionPane.showMessageDialog(this, "Nhập Đầy Đủ Thông Tin Sinh Viên!");
                return;
            }

            int tuoi = Integer.parseInt(tuoiStr);
            String gioiTinh = jRadioButton1.isSelected() ? "Nam" : "Nữ";

            SinhVien sv = new SinhVien(hoTen, tuoi, gioiTinh);

            if (sinhVienDAO.insert(sv)) {
                JOptionPane.showMessageDialog(this, "Thêm Thành Công Sinh Viên!");
                loadData();
                nhapMoi();
            } else {
                JOptionPane.showMessageDialog(this, "Lỗi!");
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "Tuổi phải là số nguyên!");
        }
    }

    // Sửa sinh viên
    private void suaSinhVien() {
        try {
            String maSVStr = txtMaSV.getText().trim();
            String hoTen = txtHoTen.getText().trim();
            String tuoiStr = txtTuoi.getText().trim();

            if (maSVStr.isEmpty() || hoTen.isEmpty() || tuoiStr.isEmpty()) {
                JOptionPane.showMessageDialog(this, " Chọn sinh viên cần sửa và nhập đầy đủ thông tin!");
                return;
            }

            int maSV = Integer.parseInt(maSVStr);
            int tuoi = Integer.parseInt(tuoiStr);
            String gioiTinh = jRadioButton1.isSelected() ? "Nam" : "Nữ";

            SinhVien sv = new SinhVien(maSV, hoTen, tuoi, gioiTinh);

            if (sinhVienDAO.update(sv)) {
                JOptionPane.showMessageDialog(this, "Cập nhật sinh viên thành công!");
                loadData();
                nhapMoi();
            } else {
                JOptionPane.showMessageDialog(this, "Cập nhật sinh viên thất bại!");
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "Mã SV và tuổi phải là số nguyên!");
        }
    }

    // Xóa sinh viên
    private void xoaSinhVien() {
        try {
            String maSVStr = txtMaSV.getText().trim();

            if (maSVStr.isEmpty()) {
                JOptionPane.showMessageDialog(this, "Vui lòng chọn sinh viên cần xóa!");
                return;
            }

            int maSV = Integer.parseInt(maSVStr);

            int confirm = JOptionPane.showConfirmDialog(this,
                "Bạn có chắc chắn muốn xóa sinh viên này?",
                "Xác nhận xóa",
                JOptionPane.YES_NO_OPTION);

            if (confirm == JOptionPane.YES_OPTION) {
                if (sinhVienDAO.delete(maSV)) {
                    JOptionPane.showMessageDialog(this, "Xóa sinh viên thành công!");
                    loadData();
                    nhapMoi();
                } else {
                    JOptionPane.showMessageDialog(this, "Xóa sinh viên thất bại!");
                }
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "Mã SV phải là số nguyên!");
        }
    }

    // Nhập mới (clear form)
    private void nhapMoi() {
        txtMaSV.setText("");
        txtHoTen.setText("");
        txtTuoi.setText("");
        jRadioButton1.setSelected(true);
        txtTimTen.setText("");
        cboLocTheoGioiTinh.setSelectedIndex(0);
        tblSinhVien.clearSelection();
    }

    // Tìm kiếm theo tên
    private void timKiem() {
        String keyword = txtTimTen.getText().trim();
        if (keyword.isEmpty()) {
            loadData();
        } else {
            List<SinhVien> list = sinhVienDAO.searchByName(keyword);
            displayData(list);
        }
    }

    // Lọc theo giới tính
    private void locTheoGioiTinh() {
        String selectedGender = (String) cboLocTheoGioiTinh.getSelectedItem();
        if ("Tất cả".equals(selectedGender)) {
            loadData();
        } else {
            List<SinhVien> list = sinhVienDAO.filterByGender(selectedGender);
            displayData(list);
        }
    }

    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JButton btnNhapMoi;
    private javax.swing.JButton btnSua;
    private javax.swing.JButton btnThem;
    private javax.swing.JButton btnTim;
    private javax.swing.JButton btnXoa;
    private javax.swing.JComboBox<String> cboLocTheoGioiTinh;
    private javax.swing.JLabel jLabel1;
    private javax.swing.JLabel jLabel2;
    private javax.swing.JLabel jLabel3;
    private javax.swing.JLabel jLabel4;
    private javax.swing.JLabel jLabel5;
    private javax.swing.JLabel jLabel6;
    private javax.swing.JPanel jPanel2;
    private javax.swing.JRadioButton jRadioButton1;
    private javax.swing.JScrollPane jScrollPane1;
    private javax.swing.JPanel rdbNam;
    private javax.swing.JRadioButton rdbNu;
    private javax.swing.JTable tblSinhVien;
    private javax.swing.JTextField txtHoTen;
    private javax.swing.JTextField txtMaSV;
    private javax.swing.JTextField txtTimTen;
    private javax.swing.JTextField txtTuoi;
    // End of variables declaration//GEN-END:variables
}
